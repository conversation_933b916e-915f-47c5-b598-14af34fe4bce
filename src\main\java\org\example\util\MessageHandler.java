package org.example.util;

/**
 * 消息处理器接口
 * 用于处理从Kafka消费的消息
 */
@FunctionalInterface
public interface MessageHandler {
    
    /**
     * 处理消息
     * @param topic 消息主题
     * @param key 消息键
     * @param value 消息值
     * @param partition 分区
     * @param offset 偏移量
     */
    void handle(String topic, String key, String value, int partition, long offset);
    
    /**
     * 处理消息异常
     * @param topic 消息主题
     * @param key 消息键
     * @param value 消息值
     * @param partition 分区
     * @param offset 偏移量
     * @param exception 异常
     */
    default void handleError(String topic, String key, String value, int partition, long offset, Exception exception) {
        // 默认实现：打印错误日志
        System.err.printf("处理消息失败 - Topic: %s, Partition: %d, Offset: %d, Error: %s%n", 
                         topic, partition, offset, exception.getMessage());
    }
}
