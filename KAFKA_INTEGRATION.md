# B站弹幕Kafka集成说明

## 功能概述

本项目已集成Kafka功能，实现了完整的弹幕采集和处理流程：
1. **弹幕采集**: 通过WebSocket连接B站直播间，接收弹幕消息
2. **消息推送**: 将弹幕消息推送到Kafka Topic
3. **消息消费**: 从Kafka消费弹幕消息并进行处理

## 系统架构

```
B站直播间 → BiliBiliDanmuClient → Kafka Topic → DanmuMessageConsumer → 业务处理
```

## 配置说明

### 1. Kafka配置文件

配置文件位置：`src/main/resources/kafka.properties`

```properties
bootstrap.servers=localhost:9092
# 弹幕消息topic
danmu.topic=bilibili-danmu
# 消费者组ID
consumer.group.id=danmu-consumer-group
```

### 2. 主要配置项

- `bootstrap.servers`: Kafka集群地址，默认为localhost:9092
- `danmu.topic`: 弹幕消息推送的Topic名称，默认为bilibili-danmu
- `consumer.group.id`: 消费者组ID，默认为danmu-consumer-group

## 使用方法

### 1. 启动Kafka服务

确保Kafka服务已启动并运行在配置的地址上。

### 2. 创建Topic（可选）

```bash
# 创建弹幕消息Topic
kafka-topics.sh --create --topic bilibili-danmu --bootstrap-server localhost:9092 --partitions 3 --replication-factor 1
```

### 3. 运行弹幕采集程序

#### 方式一：使用整合服务（推荐）

```java
public static void main(String[] args) {
    // 使用整合服务，自动管理生产者和消费者
    DanmuCollectionService service = new DanmuCollectionService(23197314); // 替换为实际房间号
    service.start();
}
```

#### 方式二：分别启动生产者和消费者

```java
// 启动消费者
DanmuMessageConsumer consumer = new DanmuMessageConsumer();
consumer.start();

// 启动生产者
BiliBiliDanmuService service = new BiliBiliDanmuService(23197314);
service.start();
```

### 4. 消费Kafka消息

可以使用Kafka消费者来接收弹幕消息：

```bash
# 使用命令行消费者查看消息
kafka-console-consumer.sh --topic bilibili-danmu --bootstrap-server localhost:9092 --from-beginning
```

## 消息格式

推送到Kafka的消息是完整的B站弹幕协议JSON格式，包含以下主要信息：

```json
{
  "cmd": "DANMU_MSG",
  "info": [
    [时间戳, 弹幕类型, 字体大小, 颜色, 发送时间, ...],
    "弹幕内容",
    [用户ID, "用户名", 是否管理员, ...],
    [...],
    ...
  ],
  ...
}
```

## 技术实现

### 核心类说明

1. **BiliBiliDanmuClient**: WebSocket客户端，负责接收弹幕消息并推送到Kafka
2. **DanmuMessageConsumer**: 弹幕消息消费者，实现MessageHandler接口处理弹幕业务逻辑
3. **DanmuCollectionService**: 整合服务，统一管理生产者和消费者
4. **KafkaUtils**: Kafka工具类，提供Producer和Consumer的抽象封装
   - **GenericKafkaConsumer**: 通用Kafka消费者内部类
   - **MessageHandler**: 消息处理器接口
5. **ConfigLoader**: 配置文件加载器

### 关键方法

**BiliBiliDanmuClient**:
- `sendToKafka(JSONObject messageInfo)`: 将弹幕消息异步发送到Kafka
- `closeResources()`: 关闭相关资源，包括定时任务调度器

**DanmuMessageConsumer**:
- `start()`: 启动消费者（实际在构造函数中已启动）
- `stop()`: 停止消费者并清理资源
- `handle(...)`: 实现MessageHandler接口，处理Kafka消息
- `processDanmuMessage(String messageJson)`: 处理单条弹幕消息
- `handleDanmuMessage(...)`: 处理解析后的弹幕数据

**KafkaUtils**:
- `createKafkaConsumer(String groupId)`: 创建Kafka消费者
- `startConsumer(...)`: 启动通用消费者
- `GenericKafkaConsumer`: 通用消费者内部类，支持自定义消息处理器

**DanmuCollectionService**:
- `start()`: 启动整个采集系统（消费者+生产者）
- `stop()`: 停止整个采集系统

## 注意事项

1. **网络连接**: 需要稳定的网络连接到B站直播服务器
2. **Kafka服务**: 确保Kafka服务正常运行
3. **Topic创建**: 建议预先创建Topic，避免自动创建时的配置问题
4. **资源管理**: 程序会在WebSocket连接关闭时自动清理资源
5. **异步发送**: 使用异步方式发送消息到Kafka，避免阻塞弹幕接收

## 扩展功能

可以根据需要扩展以下功能：

1. **消息过滤**: 在发送到Kafka前过滤特定类型的消息
2. **消息转换**: 将原始JSON转换为自定义格式
3. **多Topic支持**: 根据消息类型发送到不同的Topic
4. **批量发送**: 累积多条消息后批量发送，提高性能

## 故障排查

1. **连接失败**: 检查Kafka服务状态和网络连接
2. **消息丢失**: 检查Kafka Producer配置和Topic状态
3. **性能问题**: 考虑调整批量大小和发送频率
