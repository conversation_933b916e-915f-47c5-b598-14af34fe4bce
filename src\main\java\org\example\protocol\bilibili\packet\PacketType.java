package org.example.protocol.bilibili.packet;

public enum PacketType {
    /**
     * 心跳包
     */
    HEARTBEAT(2L, "心跳包"),

    /**
     * 心跳包回复（人气值）
     */
    HEARTBEAT_RESPONSE(3L, "心跳包回复（人气值）"),

    /**
     * 普通包（命令）
     */
    COMMAND(5L, "普通包（命令）"),

    /**
     * 认证包
     */
    AUTHENTICATION(7L, "认证包"),

    /**
     * 认证包回复
     */
    AUTHENTICATION_RESPONSE(8L, "认证包回复");

    private final long code;
    private final String description;

    PacketType(long code, String description) {
        this.code = code;
        this.description = description;
    }

    public long getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }

    /**
     * 根据code获取对应的PacketType枚举
     * @param code 封包类型操作码
     * @return 对应的PacketType枚举
     * @throws IllegalArgumentException 如果code不存在对应的枚举
     */
    public static PacketType fromCode(long code) {
        for (PacketType type : PacketType.values()) {
            if (type.getCode() == code) {
                return type;
            }
        }
        throw new IllegalArgumentException("Unknown packet type code: " + code);
    }
}

