package org.example.protocol.bilibili.packet;

import java.nio.ByteBuffer;

public class TestInt2Unint {
    public static void main(String[] args) {
        // 创建一个ByteBuffer
        ByteBuffer buffer = ByteBuffer.allocate(4);

        // 要写入的uint32值
        long unsignedInt = 0xF2345678L; // 无符号整数

        // 将uint32值转换为int并写入ByteBuffer
        int signedInt = (int) (unsignedInt & 0xFFFFFFFFL); // 确保只取低32位
        System.out.println(signedInt);
        buffer.putInt(signedInt);

        // 切换到读模式并读取验证
        buffer.flip();
        int readInt = buffer.getInt();
        long readUnsignedInt = readInt & 0xFFFFFFFFL; // 转换为无符号long

        System.out.println("Written unsigned int: " + unsignedInt);
        System.out.println("Read unsigned int: " + readUnsignedInt);
    }
}
