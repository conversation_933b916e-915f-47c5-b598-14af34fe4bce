package org.example.util;

import java.io.IOException;
import java.time.Instant;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

import org.elasticsearch.action.index.IndexRequest;
import org.elasticsearch.action.index.IndexResponse;
import org.elasticsearch.client.RequestOptions;
import org.elasticsearch.client.RestHighLevelClient;
import org.elasticsearch.common.xcontent.XContentType;
import org.junit.jupiter.api.Test;

import com.alibaba.fastjson2.JSON;

import cn.hutool.core.lang.UUID;

public class ESClientUtilsTest {

    @Test
    public void testESClient() throws IOException{
        RestHighLevelClient client = ESClientUtils.getClient();
        Map<String,Object> danmu_data = new HashMap<>();
        danmu_data.put("app_key","douyu");
        danmu_data.put("danmu_msg","测试弹幕数据推送es");
        danmu_data.put("danmu_timestamp",Instant.now().toEpochMilli());
        danmu_data.put("doc_create_date",new Date());
        danmu_data.put("room_id","12308");
        danmu_data.put("user_nickname","爷傲奈我何");
        
        
        // 创建index request
        IndexRequest indexRequest = new IndexRequest("danmu_index")
            .id(UUID.randomUUID().toString())
            .source(danmu_data);

        //发送请求，写入数据
        IndexResponse resp = client.index(indexRequest, RequestOptions.DEFAULT);
        System.out.println(resp.getResult());
    }
}
