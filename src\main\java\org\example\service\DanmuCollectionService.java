package org.example.service;

import lombok.extern.slf4j.Slf4j;

/**
 * 弹幕采集服务
 * 整合弹幕生产者和消费者，提供完整的弹幕采集和处理功能
 */
@Slf4j
public class DanmuCollectionService {
    
    private BiliBiliDanmuService danmuService;
    private DanmuMessageConsumer messageConsumer;
    private Integer roomId;
    
    public DanmuCollectionService(Integer roomId) {
        this.roomId = roomId;
        this.danmuService = new BiliBiliDanmuService(roomId);
        this.messageConsumer = new DanmuMessageConsumer();
    }
    
    /**
     * 启动弹幕采集服务
     * 先启动消费者，再启动生产者
     */
    public void start() {
        log.info("正在启动弹幕采集服务，房间号: {}", roomId);
        
        try {
            // 先启动消费者
            log.info("启动弹幕消息消费者...");
            messageConsumer.start();
            
            // 等待消费者完全启动
            Thread.sleep(2000);
            
            // 再启动弹幕采集（生产者）
            log.info("启动弹幕采集服务...");
            danmuService.start();
            
        } catch (Exception e) {
            log.error("启动弹幕采集服务失败", e);
            stop();
            throw new RuntimeException("启动弹幕采集服务失败", e);
        }
    }
    
    /**
     * 停止弹幕采集服务
     */
    public void stop() {
        log.info("正在停止弹幕采集服务...");
        
        try {
            // 停止消费者
            if (messageConsumer != null) {
                messageConsumer.stop();
            }
            
            // 注意：BiliBiliDanmuService的stop方法需要根据实际情况实现
            // 这里暂时没有实现，因为原始代码中使用的是无限循环
            
            log.info("弹幕采集服务已停止");
        } catch (Exception e) {
            log.error("停止弹幕采集服务时发生异常", e);
        }
    }
    
    /**
     * 检查服务是否正在运行
     * @return true if running
     */
    public boolean isRunning() {
        return messageConsumer != null && messageConsumer.isRunning();
    }
    
    /**
     * 主方法，用于启动完整的弹幕采集系统
     */
    public static void main(String[] args) {
        // 可以通过命令行参数传入房间号
        Integer roomId = 7777; // 默认房间号
        
        if (args.length > 0) {
            try {
                roomId = Integer.parseInt(args[0]);
            } catch (NumberFormatException e) {
                log.warn("无效的房间号参数: {}, 使用默认房间号: {}", args[0], roomId);
            }
        }
        
        DanmuCollectionService service = new DanmuCollectionService(roomId);
        
        // 添加关闭钩子
        Runtime.getRuntime().addShutdownHook(new Thread(() -> {
            log.info("接收到关闭信号，正在停止弹幕采集服务...");
            service.stop();
        }));
        
        try {
            // 启动服务
            service.start();
            
            // 保持主线程运行
            Thread.currentThread().join();
            
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            log.info("主线程被中断，停止服务");
            service.stop();
        } catch (Exception e) {
            log.error("弹幕采集服务运行异常", e);
            service.stop();
        }
    }
}
