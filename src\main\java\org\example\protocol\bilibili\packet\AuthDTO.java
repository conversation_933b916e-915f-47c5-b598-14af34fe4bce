package org.example.protocol.bilibili.packet;

import com.alibaba.fastjson2.annotation.JSONField;
import com.alibaba.fastjson2.annotation.JSONType;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@JSONType(orders={"uid","roomid","protover","buvid","platform","type","key"})
public class AuthDTO {
    private Integer uid = 0;
    @JSONField(name = "roomid")
    private Integer roomId;
    private Integer protover;
    private String buvid;
    private String platform = "web";
    private Long type;
    private String key;


}
