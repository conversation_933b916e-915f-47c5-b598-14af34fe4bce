package org.example.protocol.bilibili.packet;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;

import org.brotli.dec.BrotliInputStream;

public class BrotliComprssUtils {

    /**
     * 解压缩
     * @param compressedData
     * @return
     * @throws IOException 
     */
    public static byte[] decompress(byte[] compressedData) throws IOException {
        // Implement Brotli decompression logic here
        // This is a placeholder implementation
        try(
            ByteArrayInputStream byteArrayInputStream = new ByteArrayInputStream(compressedData);
            BrotliInputStream brotliInputStream = new BrotliInputStream(byteArrayInputStream);
            ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream()
        ){
            byte[] buffer = new byte[1024];
            int bytesRead;
            while((bytesRead = brotliInputStream.read(buffer)) != -1){
                byteArrayOutputStream.write(buffer,0,bytesRead);
            }
            return byteArrayOutputStream.toByteArray();
        }

    }

    /**
     * 压缩
     * @param data
     * @return
     */
    public static byte[] compress(byte[] data){
        // Implement Brotli compression logic here
        // This is a placeholder implementation
        return new byte[0];
    }
}
