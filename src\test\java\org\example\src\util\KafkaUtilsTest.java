package org.example.src.util;

import org.apache.kafka.clients.producer.KafkaProducer;
import org.apache.kafka.clients.producer.ProducerRecord;
import org.example.util.KafkaUtils;
import org.junit.jupiter.api.Test;

public class KafkaUtilsTest {

    private final String TOPIC = "test-topic";
    @Test
    public void testSendMessage(){
        KafkaProducer<String,String> producer = KafkaUtils.getProducerInstance();
        ProducerRecord<String,String> msgRecord = new ProducerRecord<>(TOPIC,"Hello World,你好 中国");
        KafkaUtils.sendMessage(producer,msgRecord);
        
    }
}
