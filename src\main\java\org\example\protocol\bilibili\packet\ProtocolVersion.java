package org.example.protocol.bilibili.packet;

public enum ProtocolVersion {
    /**
     * 普通包正文不使用压缩
     */
    PLAIN_TEXT_NORMAL(0, "普通包正文不使用压缩"),

    /**
     * 心跳及认证包正文不使用压缩
     */
    PLAIN_TEXT_AUTHENTICATION(1, "心跳及认证包正文不使用压缩"),

    /**
     * 普通包正文使用zlib压缩
     */
    ZLIB_COMPRESSED_NORMAL(2, "普通包正文使用zlib压缩"),

    /**
     * 普通包正文使用brotli压缩,解压为一个带头部的协议0普通包
     */
    BROTLI_COMPRESSED_NORMAL(3, "普通包正文使用brotli压缩,解压为一个带头部的协议0普通包");

    private final int code;
    private final String description;

    ProtocolVersion(int code, String description) {
        this.code = code;
        this.description = description;
    }

    public int getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }

    /**
     * 根据code获取对应的ProtocolVersion枚举
     * @param code 协议版本号
     * @return 对应的ProtocolVersion枚举
     * @throws IllegalArgumentException 如果code不存在对应的枚举
     */
    public static ProtocolVersion fromCode(int code) {
        for (ProtocolVersion version : ProtocolVersion.values()) {
            if (version.getCode() == code) {
                return version;
            }
        }
        throw new IllegalArgumentException("Unknown protocol version code: " + code);
    }
}
