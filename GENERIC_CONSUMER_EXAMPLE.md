# 通用Kafka消费者使用示例

## 概述

KafkaUtils现在提供了通用的Kafka消费者抽象，可以轻松创建自定义的消息处理逻辑。

## 核心组件

### 1. MessageHandler接口

```java
@FunctionalInterface
public interface MessageHandler {
    void handle(String topic, String key, String value, int partition, long offset);
    
    default void handleError(String topic, String key, String value, int partition, long offset, Exception exception) {
        // 默认错误处理
    }
}
```

### 2. GenericKafkaConsumer类

通用Kafka消费者，支持：
- 自动消息轮询
- 异常处理
- 优雅关闭
- 自定义消息处理器

## 使用示例

### 示例1：简单消息处理

```java
import org.example.util.KafkaUtils;
import org.example.util.MessageHandler;
import java.util.Collections;

public class SimpleConsumerExample {
    public static void main(String[] args) {
        // 创建消息处理器
        MessageHandler handler = (topic, key, value, partition, offset) -> {
            System.out.printf("收到消息: %s%n", value);
        };
        
        // 启动消费者
        KafkaUtils.GenericKafkaConsumer consumer = KafkaUtils.startConsumer(
            Collections.singletonList("my-topic"),
            "my-group",
            handler
        );
        
        // 程序运行...
        
        // 关闭消费者
        consumer.stop();
    }
}
```

### 示例2：复杂业务处理

```java
public class BusinessConsumerExample {
    public static void main(String[] args) {
        MessageHandler businessHandler = new MessageHandler() {
            @Override
            public void handle(String topic, String key, String value, int partition, long offset) {
                try {
                    // 解析JSON消息
                    JSONObject message = JSON.parseObject(value);
                    String type = message.getString("type");
                    
                    switch (type) {
                        case "ORDER":
                            processOrder(message);
                            break;
                        case "PAYMENT":
                            processPayment(message);
                            break;
                        default:
                            System.out.println("未知消息类型: " + type);
                    }
                } catch (Exception e) {
                    System.err.println("处理消息失败: " + e.getMessage());
                }
            }
            
            @Override
            public void handleError(String topic, String key, String value, int partition, long offset, Exception exception) {
                // 自定义错误处理
                System.err.printf("消息处理异常 - Topic: %s, Offset: %d, Error: %s%n", 
                                 topic, offset, exception.getMessage());
                
                // 可以发送到死信队列或记录到数据库
                sendToDeadLetterQueue(topic, key, value, exception);
            }
            
            private void processOrder(JSONObject order) {
                // 处理订单逻辑
                System.out.println("处理订单: " + order.getString("orderId"));
            }
            
            private void processPayment(JSONObject payment) {
                // 处理支付逻辑
                System.out.println("处理支付: " + payment.getString("paymentId"));
            }
            
            private void sendToDeadLetterQueue(String topic, String key, String value, Exception exception) {
                // 发送到死信队列的逻辑
            }
        };
        
        KafkaUtils.GenericKafkaConsumer consumer = KafkaUtils.startConsumer(
            Arrays.asList("order-topic", "payment-topic"),
            "business-group",
            businessHandler
        );
        
        // 添加关闭钩子
        Runtime.getRuntime().addShutdownHook(new Thread(consumer::stop));
        
        try {
            Thread.currentThread().join();
        } catch (InterruptedException e) {
            consumer.stop();
        }
    }
}
```

### 示例3：自定义配置

```java
public class CustomConfigConsumerExample {
    public static void main(String[] args) {
        // 自定义消费者配置
        Properties customProps = new Properties();
        customProps.put(ConsumerConfig.MAX_POLL_RECORDS_CONFIG, "100");
        customProps.put(ConsumerConfig.FETCH_MIN_BYTES_CONFIG, "1024");
        customProps.put(ConsumerConfig.FETCH_MAX_WAIT_MS_CONFIG, "500");
        
        MessageHandler handler = (topic, key, value, partition, offset) -> {
            // 批量处理逻辑
            processBatch(value);
        };
        
        KafkaUtils.GenericKafkaConsumer consumer = KafkaUtils.startConsumer(
            Collections.singletonList("high-volume-topic"),
            "batch-group",
            handler,
            customProps
        );
        
        // 运行消费者...
        consumer.stop();
    }
    
    private static void processBatch(String value) {
        // 批量处理逻辑
    }
}
```

## 弹幕消费者实现

弹幕消费者现在使用了这个抽象：

```java
@Slf4j
public class DanmuMessageConsumer implements MessageHandler {
    private KafkaUtils.GenericKafkaConsumer kafkaConsumer;
    
    public DanmuMessageConsumer() {
        this.kafkaConsumer = KafkaUtils.startConsumer(
            Collections.singletonList(DANMU_TOPIC),
            CONSUMER_GROUP_ID,
            this  // 实现了MessageHandler接口
        );
    }
    
    @Override
    public void handle(String topic, String key, String value, int partition, long offset) {
        processDanmuMessage(value);
    }
    
    @Override
    public void handleError(String topic, String key, String value, int partition, long offset, Exception exception) {
        log.error("处理弹幕消息失败", exception);
    }
    
    // 弹幕处理逻辑...
}
```

## 优势

1. **代码复用**: 通用消费者可以用于不同的业务场景
2. **简化开发**: 只需实现MessageHandler接口即可
3. **统一管理**: 所有消费者配置和生命周期管理统一
4. **错误处理**: 内置异常处理机制
5. **易于测试**: 接口抽象便于单元测试

## 最佳实践

1. **异常处理**: 总是实现handleError方法
2. **资源管理**: 使用try-with-resources或关闭钩子
3. **批量处理**: 对于高吞吐量场景，考虑批量处理
4. **监控**: 添加适当的日志和监控
5. **配置优化**: 根据业务需求调整消费者配置
