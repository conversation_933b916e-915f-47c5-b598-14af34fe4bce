# 使用示例

## 快速开始

### 1. 启动Kafka服务

```bash
# 启动Zookeeper
bin/zookeeper-server-start.sh config/zookeeper.properties

# 启动Kafka
bin/kafka-server-start.sh config/server.properties
```

### 2. 创建Topic（可选）

```bash
# 创建弹幕消息Topic
bin/kafka-topics.sh --create --topic bilibili-danmu --bootstrap-server localhost:9092 --partitions 3 --replication-factor 1

# 查看Topic列表
bin/kafka-topics.sh --list --bootstrap-server localhost:9092
```

### 3. 运行弹幕采集系统

#### 方式一：使用整合服务（推荐）

```java
// 直接运行DanmuCollectionService的main方法
public class Application {
    public static void main(String[] args) {
        // 可以通过命令行参数传入房间号
        // java -cp target/classes org.example.service.DanmuCollectionService 23197314
        DanmuCollectionService.main(args);
    }
}
```

#### 方式二：分别启动

**终端1 - 启动消费者：**
```java
public class ConsumerApp {
    public static void main(String[] args) {
        DanmuMessageConsumer.main(args);
    }
}
```

**终端2 - 启动生产者：**
```java
public class ProducerApp {
    public static void main(String[] args) {
        BiliBiliDanmuService service = new BiliBiliDanmuService(23197314);
        service.start();
    }
}
```

### 4. 查看消息

```bash
# 使用命令行消费者查看弹幕消息
bin/kafka-console-consumer.sh --topic bilibili-danmu --bootstrap-server localhost:9092 --from-beginning
```

## 自定义配置

### 修改配置文件

编辑 `src/main/resources/kafka.properties`:

```properties
# Kafka服务器地址
bootstrap.servers=localhost:9092

# 弹幕消息Topic
danmu.topic=my-danmu-topic

# 消费者组ID
consumer.group.id=my-consumer-group
```

### 自定义消息处理逻辑

继承或修改 `DanmuMessageConsumer` 类：

```java
public class CustomDanmuConsumer extends DanmuMessageConsumer {
    
    @Override
    protected void handleDanmuMessage(String userName, String danmuMsg, long timestamp, JSONObject fullMessage) {
        // 调用父类方法（打印日志）
        super.handleDanmuMessage(userName, danmuMsg, timestamp, fullMessage);
        
        // 自定义处理逻辑
        if (danmuMsg.contains("关键词")) {
            // 特殊处理
            saveToDatabase(userName, danmuMsg, timestamp);
        }
        
        // 实时统计
        updateStatistics(userName, danmuMsg);
    }
    
    private void saveToDatabase(String userName, String danmuMsg, long timestamp) {
        // 保存到数据库
    }
    
    private void updateStatistics(String userName, String danmuMsg) {
        // 更新统计信息
    }
}
```

## 监控和调试

### 查看消费者组状态

```bash
# 查看消费者组信息
bin/kafka-consumer-groups.sh --bootstrap-server localhost:9092 --describe --group danmu-consumer-group
```

### 查看Topic详情

```bash
# 查看Topic详细信息
bin/kafka-topics.sh --describe --topic bilibili-danmu --bootstrap-server localhost:9092
```

### 日志配置

修改 `src/main/resources/logback.xml` 来调整日志级别：

```xml
<!-- 设置弹幕消费者为DEBUG级别 -->
<logger name="org.example.service.DanmuMessageConsumer" level="DEBUG"/>

<!-- 设置Kafka相关日志级别 -->
<logger name="org.apache.kafka" level="WARN"/>
```

## 性能优化

### 消费者配置优化

```properties
# 批量拉取大小
fetch.min.bytes=1024
fetch.max.wait.ms=500

# 并发处理
max.poll.records=100
```

### 生产者配置优化

```properties
# 批量发送
batch.size=16384
linger.ms=5

# 压缩
compression.type=snappy
```

## 故障排查

### 常见问题

1. **连接失败**: 检查Kafka服务是否启动，端口是否正确
2. **消息丢失**: 检查Topic是否存在，消费者组配置是否正确
3. **重复消费**: 检查消费者组ID是否唯一
4. **性能问题**: 调整批量大小和并发参数

### 调试命令

```bash
# 检查Kafka服务状态
netstat -an | grep 9092

# 查看日志
tail -f logs/server.log

# 测试连接
bin/kafka-broker-api-versions.sh --bootstrap-server localhost:9092
```
