<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="bc536bb2-46cf-4357-8487-e8d3f7b4b755" name="Changes" comment="初始化项目">
      <change beforePath="$PROJECT_DIR$/pom.xml" beforeDir="false" afterPath="$PROJECT_DIR$/pom.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/org/example/service/BiliBiliDanmuClient.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/org/example/service/BiliBiliDanmuClient.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/org/example/service/DanmuMessageConsumer.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/org/example/service/DanmuMessageConsumer.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/resources/elasticsearch.properties" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/resources/elasticsearch.properties" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/resources/kafka.properties" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/resources/kafka.properties" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="FileTemplateManagerImpl">
    <option name="RECENT_TEMPLATES">
      <list>
        <option value="Interface" />
        <option value="JUnit5 Test Class" />
        <option value="Enum" />
        <option value="Class" />
      </list>
    </option>
  </component>
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$" />
  </component>
  <component name="MarkdownSettingsMigration">
    <option name="stateVersion" value="1" />
  </component>
  <component name="ProjectId" id="2vuAQmlRp67VnoDEhbAac069ZMp" />
  <component name="ProjectLevelVcsManager" settingsEditedManually="true" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent">{
  &quot;keyToString&quot;: {
    &quot;RunOnceActivity.OpenProjectViewOnStart&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.ShowReadmeOnStart&quot;: &quot;true&quot;,
    &quot;SHARE_PROJECT_CONFIGURATION_FILES&quot;: &quot;true&quot;,
    &quot;com.intellij.testIntegration.createTest.CreateTestDialog.defaultLibrary&quot;: &quot;JUnit5&quot;,
    &quot;com.intellij.testIntegration.createTest.CreateTestDialog.defaultLibrarySuperClass.JUnit5&quot;: &quot;&quot;,
    &quot;project.structure.last.edited&quot;: &quot;Modules&quot;,
    &quot;project.structure.proportion&quot;: &quot;0.0&quot;,
    &quot;project.structure.side.proportion&quot;: &quot;0.0&quot;
  }
}</component>
  <component name="RecentsManager">
    <key name="CreateTestDialog.Recents.Supers">
      <recent name="" />
    </key>
    <key name="CreateTestDialog.RecentsKey">
      <recent name="org.example.src.service" />
    </key>
  </component>
  <component name="RunManager" selected="Application.BiliBiliDanmuService">
    <configuration name="BiliBiliDanmuService" type="Application" factoryName="Application" temporary="true" nameIsGenerated="true">
      <option name="MAIN_CLASS_NAME" value="org.example.src.service.BiliBiliDanmuService" />
      <module name="live-danmu-data-collect" />
      <extension name="coverage">
        <pattern>
          <option name="PATTERN" value="org.example.src.service.*" />
          <option name="ENABLED" value="true" />
        </pattern>
      </extension>
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="Main" type="Application" factoryName="Application" temporary="true" nameIsGenerated="true">
      <option name="MAIN_CLASS_NAME" value="org.example.Main" />
      <module name="live-danmu-data-collect" />
      <extension name="coverage">
        <pattern>
          <option name="PATTERN" value="org.example.*" />
          <option name="ENABLED" value="true" />
        </pattern>
      </extension>
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="BiliBiliDanmuClientTest.DanmuClientTest" type="JUnit" factoryName="JUnit" temporary="true" nameIsGenerated="true">
      <module name="live-danmu-data-collect" />
      <extension name="coverage">
        <pattern>
          <option name="PATTERN" value="org.example.src.service.*" />
          <option name="ENABLED" value="true" />
        </pattern>
      </extension>
      <option name="PACKAGE_NAME" value="org.example.src.service" />
      <option name="MAIN_CLASS_NAME" value="org.example.src.service.BiliBiliDanmuClientTest" />
      <option name="METHOD_NAME" value="DanmuClientTest" />
      <option name="TEST_OBJECT" value="method" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="BiliBiliDanmuServiceTest.getDanmuTest" type="JUnit" factoryName="JUnit" temporary="true" nameIsGenerated="true">
      <module name="live-danmu-data-collect" />
      <extension name="coverage">
        <pattern>
          <option name="PATTERN" value="org.example.src.service.*" />
          <option name="ENABLED" value="true" />
        </pattern>
      </extension>
      <option name="PACKAGE_NAME" value="org.example.src.service" />
      <option name="MAIN_CLASS_NAME" value="org.example.src.service.BiliBiliDanmuServiceTest" />
      <option name="METHOD_NAME" value="getDanmuTest" />
      <option name="TEST_OBJECT" value="method" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="BiliBiliDanmuServiceTest.start" type="JUnit" factoryName="JUnit" temporary="true" nameIsGenerated="true">
      <module name="live-danmu-data-collect" />
      <extension name="coverage">
        <pattern>
          <option name="PATTERN" value="org.example.src.service.*" />
          <option name="ENABLED" value="true" />
        </pattern>
      </extension>
      <option name="PACKAGE_NAME" value="org.example.src.service" />
      <option name="MAIN_CLASS_NAME" value="org.example.src.service.BiliBiliDanmuServiceTest" />
      <option name="METHOD_NAME" value="start" />
      <option name="TEST_OBJECT" value="method" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <recent_temporary>
      <list>
        <item itemvalue="Application.BiliBiliDanmuService" />
        <item itemvalue="JUnit.BiliBiliDanmuServiceTest.start" />
        <item itemvalue="JUnit.BiliBiliDanmuServiceTest.getDanmuTest" />
        <item itemvalue="JUnit.BiliBiliDanmuClientTest.DanmuClientTest" />
        <item itemvalue="Application.Main" />
      </list>
    </recent_temporary>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="application-level" UseSingleDictionary="true" transferred="true" />
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="bc536bb2-46cf-4357-8487-e8d3f7b4b755" name="Changes" comment="" />
      <created>1744984572424</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1744984572424</updated>
    </task>
    <task id="LOCAL-00001" summary="初始化项目">
      <created>1744985186015</created>
      <option name="number" value="00001" />
      <option name="presentableId" value="LOCAL-00001" />
      <option name="project" value="LOCAL" />
      <updated>1744985186015</updated>
    </task>
    <option name="localTasksCounter" value="2" />
    <servers />
  </component>
  <component name="Vcs.Log.Tabs.Properties">
    <option name="TAB_STATES">
      <map>
        <entry key="MAIN">
          <value>
            <State />
          </value>
        </entry>
      </map>
    </option>
  </component>
  <component name="VcsManagerConfiguration">
    <MESSAGE value="初始化项目" />
    <option name="LAST_COMMIT_MESSAGE" value="初始化项目" />
  </component>
</project>