package org.example.util;

import org.junit.jupiter.api.Test;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Collections;

/**
 * KafkaUtils测试类
 */
public class KafkaUtilsTest {
    
    private static final Logger logger = LoggerFactory.getLogger(KafkaUtilsTest.class);
    
    @Test
    public void testGenericConsumer() {
        logger.info("开始测试通用Kafka消费者");
        
        try {
            // 创建消息处理器
            MessageHandler handler = new MessageHandler() {
                @Override
                public void handle(String topic, String key, String value, int partition, long offset) {
                    logger.info("收到消息 - Topic: {}, Key: {}, Value: {}, Partition: {}, Offset: {}", 
                               topic, key, value, partition, offset);
                }
                
                @Override
                public void handleError(String topic, String key, String value, int partition, long offset, Exception exception) {
                    logger.error("处理消息失败 - Topic: {}, Key: {}, Value: {}, Partition: {}, Offset: {}", 
                                topic, key, value, partition, offset, exception);
                }
            };
            
            // 启动消费者
            KafkaUtils.GenericKafkaConsumer consumer = KafkaUtils.startConsumer(
                Collections.singletonList("test-topic"), 
                "test-group", 
                handler
            );
            
            logger.info("通用消费者创建成功");
            
            // 注意：这个测试需要实际的Kafka服务
            // Thread.sleep(5000); // 运行5秒
            // consumer.stop();
            
        } catch (Exception e) {
            logger.error("测试通用Kafka消费者失败", e);
        }
        
        logger.info("通用Kafka消费者测试完成");
    }
    
    @Test
    public void testDanmuConsumerWithKafkaUtils() {
        logger.info("开始测试使用KafkaUtils的弹幕消费者");
        
        try {
            org.example.service.DanmuMessageConsumer consumer = new org.example.service.DanmuMessageConsumer();
            logger.info("弹幕消费者创建成功");
            
            // 注意：这个测试需要实际的Kafka服务
            // consumer.start();
            // Thread.sleep(5000); // 运行5秒
            // consumer.stop();
            
        } catch (Exception e) {
            logger.error("测试弹幕消费者失败", e);
        }
        
        logger.info("弹幕消费者测试完成");
    }
}
