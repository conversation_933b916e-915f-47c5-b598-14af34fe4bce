package org.example.protocol.bilibili.packet;

public class HeartBeatRespDataPacket extends BiliBiliDataPacket {
    /**
     * 人气值
     */
    private Long popularityValue;

    public HeartBeatRespDataPacket(ProtocolVersion protocolVersion,
                              PacketType packetType,
                              Long popularityValue,
                              String content,
                              Long sequence){
        super(protocolVersion,packetType,content,sequence);
        this.popularityValue = popularityValue;
    }

    public Long getPopularityValue() {
        return popularityValue;
    }

    

}
