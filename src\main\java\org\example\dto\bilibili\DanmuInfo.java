package org.example.dto.bilibili;

import com.alibaba.fastjson2.annotation.JSONField;
import lombok.Builder;
import lombok.Data;

import java.util.List;

@Data
public class DanmuInfo {
    private String group;
    @JSONField(name = "business_id")
    private Integer businessId;
    @JSONField(name="refresh_row_factor")
    private Integer refreshRowFactor;

    @JSONField(name="refresh_rate")
    private Integer refreshRate;
    @JSONField(name="max_delay")
    private Integer maxDelay;

    private String token;

    @JSONField(name="host_list")
    private List<ServerHost> hostList;

    @Data
    public static class ServerHost {
        private String host;
        private Integer port;
        private Integer wss_port;
        private Integer ws_port;
    }
}

