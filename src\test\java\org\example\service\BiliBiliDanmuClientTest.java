package org.example.service;

import org.junit.jupiter.api.Test;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * 弹幕采集系统测试类
 */
public class BiliBiliDanmuClientTest {

    private static final Logger logger = LoggerFactory.getLogger(BiliBiliDanmuClientTest.class);

    @Test
    public void testDanmuCollection() {
        logger.info("开始测试弹幕采集功能");

        // 测试房间号，可以替换为实际的B站直播间号
        Integer testRoomId = 23197314;

        try {
            BiliBiliDanmuService service = new BiliBiliDanmuService(testRoomId);

            // 注意：这个测试需要实际的网络连接和Kafka服务
            // 在实际测试环境中运行
            logger.info("创建弹幕服务成功，房间号: {}", testRoomId);

            // service.start(); // 取消注释以启动实际测试

        } catch (Exception e) {
            logger.error("测试弹幕采集功能失败", e);
        }

        logger.info("弹幕采集功能测试完成");
    }

    @Test
    public void testDanmuConsumer() {
        logger.info("开始测试弹幕消费者功能");

        try {
            DanmuMessageConsumer consumer = new DanmuMessageConsumer();
            logger.info("创建弹幕消费者成功");

            // 注意：这个测试需要实际的Kafka服务
            // consumer.start(); // 取消注释以启动实际测试
            // Thread.sleep(5000); // 运行5秒
            // consumer.stop();

        } catch (Exception e) {
            logger.error("测试弹幕消费者功能失败", e);
        }

        logger.info("弹幕消费者功能测试完成");
    }

    @Test
    public void testIntegratedService() {
        logger.info("开始测试整合服务功能");

        Integer testRoomId = 23197314;

        try {
            DanmuCollectionService service = new DanmuCollectionService(testRoomId);
            logger.info("创建整合服务成功，房间号: {}", testRoomId);

            // 注意：这个测试需要实际的网络连接和Kafka服务
            // service.start(); // 取消注释以启动实际测试
            // Thread.sleep(10000); // 运行10秒
            // service.stop();

        } catch (Exception e) {
            logger.error("测试整合服务功能失败", e);
        }

        logger.info("整合服务功能测试完成");
    }
}
