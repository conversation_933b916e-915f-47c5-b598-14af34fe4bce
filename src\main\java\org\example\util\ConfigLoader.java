package org.example.util;

import java.io.InputStream;
import java.util.Properties;

import lombok.extern.slf4j.Slf4j;

/**
 * 配置文件加载器
 */
@Slf4j
public class ConfigLoader {
    public static Properties loadProperties(String filePath){
        Properties properties = new Properties();
        try( InputStream input = ConfigLoader.class.getClassLoader().getResourceAsStream(filePath)){
            if(input == null){
                log.error("properties file not found,{}",filePath);
                throw new RuntimeException("properties file not found");
            }
            properties.load(input);
            return properties;
        }catch(Exception ex){
            log.error("load properties error,{}",filePath,ex);
            throw new RuntimeException("load properties error",ex);
        }
    }

    public static void main(String[] args) {
        Properties properties = loadProperties("kafka.properties");
        System.out.println(properties.getProperty("bootstrap.servers"));
    }
}
