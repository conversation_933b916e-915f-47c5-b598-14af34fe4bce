package org.example.protocol.bilibili.packet;

import com.alibaba.fastjson2.JSON;
import lombok.Getter;


@Getter
public class AuthPacket {
    private AuthDTO authDTO;

    private Long sequence;

    private byte[] bytes;

    public AuthPacket(AuthDTO authDTO,Long sequence){
        this.authDTO = authDTO;
        this.sequence = sequence;
    }

    public AuthPacket buildAuthPacket(){
        BiliBiliDataPacket biliBiliDataPacket = new BiliBiliDataPacket(
                ProtocolVersion.BROTLI_COMPRESSED_NORMAL,
                PacketType.AUTHENTICATION,
                JSON.toJSONString(authDTO),
                sequence);
        this.bytes = biliBiliDataPacket.buildPacket();
        return this;
    }
}
