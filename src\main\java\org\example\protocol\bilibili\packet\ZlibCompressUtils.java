package org.example.protocol.bilibili.packet;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.zip.DataFormatException;
import java.util.zip.Inflater;

public class ZlibCompressUtils {

    public static byte[] decompress(byte[] compressedData) throws IOException, DataFormatException{
        Inflater inflater = new Inflater();
        inflater.setInput(compressedData);
        try(ByteArrayOutputStream outputStream = new ByteArrayOutputStream()){
            byte[] buffer = new byte[1024];
            while(!inflater.finished()){
                int count = inflater.inflate(buffer);
                outputStream.write(buffer,0,count);
            }
            return outputStream.toByteArray();
        }finally{
            inflater.end();
        }
    }
}
